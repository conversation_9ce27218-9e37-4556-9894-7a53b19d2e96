// internal/app/events.go
package app

import (
	"sync"
	"time"

	"assistant-go/internal/modules"
	"assistant-go/internal/utils"
)

// EventBus 事件總線，負責模組間的通訊
type EventBus struct {
	subscribers map[string][]modules.EventHandler
	mutex       sync.RWMutex
	logger      *utils.Logger
}

// NewEventBus 建立新的事件總線
func NewEventBus(logger *utils.Logger) *EventBus {
	return &EventBus{
		subscribers: make(map[string][]modules.EventHandler),
		logger:      logger,
	}
}

// Subscribe 訂閱事件
func (eb *EventBus) Subscribe(eventType string, handler modules.EventHandler) {
	eb.mutex.Lock()
	defer eb.mutex.Unlock()

	eb.subscribers[eventType] = append(eb.subscribers[eventType], handler)
	eb.logger.Debug("Event handler subscribed to %s", eventType)
}

// Unsubscribe 取消訂閱事件
func (eb *EventBus) Unsubscribe(eventType string, handler modules.EventHandler) {
	eb.mutex.Lock()
	defer eb.mutex.Unlock()

	handlers := eb.subscribers[eventType]
	for i, h := range handlers {
		if h == handler {
			eb.subscribers[eventType] = append(handlers[:i], handlers[i+1:]...)
			eb.logger.Debug("Event handler unsubscribed from %s", eventType)
			break
		}
	}
}

// Publish 發布事件
func (eb *EventBus) Publish(event modules.Event) {
	eb.mutex.RLock()
	handlers := eb.subscribers[event.Type]
	eb.mutex.RUnlock()

	eb.logger.Debug("Publishing event: %s from %s", event.Type, event.Source)

	// 異步處理事件，避免阻塞
	go func() {
		for _, handler := range handlers {
			if err := handler.HandleEvent(event); err != nil {
				eb.logger.Error("Error handling event %s: %v", event.Type, err)
			}
		}
	}()
}

// 預定義的事件類型常數
const (
	EventTypeModuleStarted   = "module.started"
	EventTypeModuleStopped   = "module.stopped"
	EventTypeModuleError     = "module.error"
	EventTypeDataRefresh     = "data.refresh"
	EventTypeConfigChanged   = "config.changed"
	EventTypeUIThemeChanged  = "ui.theme.changed"
	EventTypeTaskStarted     = "task.started"
	EventTypeTaskCompleted   = "task.completed"
	EventTypeTaskFailed      = "task.failed"
	EventTypeK8sConnected    = "k8s.connected"
	EventTypeK8sDisconnected = "k8s.disconnected"
	EventTypeDBConnected     = "db.connected"
	EventTypeDBDisconnected  = "db.disconnected"
	EventTypeAIResponse      = "ai.response"
	EventTypeMCPUpdate       = "mcp.update"
)

// CreateEvent 建立新事件的輔助函式
func CreateEvent(eventType, source string, data interface{}) modules.Event {
	return modules.Event{
		Type:      eventType,
		Source:    source,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}
}
