// Package modules internal/modules/interfaces.go
package modules

import (
	"context"

	"fyne.io/fyne/v2"
)

// Module 定義了所有功能模組必須實作的介面
type Module interface {
	// 初始化模組
	Initialize(ctx context.Context) error

	// 返回模組的 UI 內容
	Content() fyne.CanvasObject

	// 重新整理模組資料
	Refresh() error

	// 關閉模組並清理資源
	Shutdown() error

	// 返回模組資訊
	Info() ModuleInfo
}

// ModuleInfo 包含模組的元資料
type ModuleInfo struct {
	ID          string
	Name        string
	Description string
	Version     string
	Author      string
}

// EventHandler 定義事件處理介面
type EventHandler interface {
	// 處理事件
	HandleEvent(event Event) error

	// 訂閱特定類型的事件
	Subscribe(eventType string) error

	// 取消訂閱
	Unsubscribe(eventType string) error
}

// Event 定義事件結構
type Event struct {
	Type      string
	Source    string
	Data      interface{}
	Timestamp int64
}
